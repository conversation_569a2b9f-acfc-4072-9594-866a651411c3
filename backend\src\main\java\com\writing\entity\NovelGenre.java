package com.writing.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 小说类型实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("novel_genres")
public class NovelGenre {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("user_id")
    private Long userId;
    
    @TableField("code")
    private String code;
    
    @TableField("name")
    private String name;
    
    @TableField("description")
    private String description;
    
    @TableField("color")
    private String color;
    
    @TableField("icon")
    private String icon;
    
    @TableField("prompt")
    private String prompt;
    
    @TableField("is_default")
    private Integer isDefault;
    
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
    
    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}
